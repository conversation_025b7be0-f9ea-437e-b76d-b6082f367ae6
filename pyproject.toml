[project]
name = "mermaid-render"
version = "1.0.0"
description = "A comprehensive, production-ready Python library for generating Mermaid diagrams with clean APIs, validation, and multiple output formats"
readme = "README.md"
requires-python = ">=3.9"
license = {text = "MIT"}
authors = [
    {name = "Mermaid Render Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "Mermaid Render Team", email = "<EMAIL>"}
]
keywords = [
    "mermaid",
    "diagrams",
    "visualization",
    "flowchart",
    "sequence",
    "uml",
    "class-diagram",
    "state-diagram",
    "er-diagram",
    "gantt",
    "mindmap",
    "svg",
    "png",
    "pdf",
    "documentation",
    "architecture"
]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Developers",
    "Intended Audience :: System Administrators",
    "Intended Audience :: Information Technology",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3 :: Only",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Scientific/Engineering :: Visualization",
    "Topic :: Documentation",
    "Topic :: Multimedia :: Graphics",
    "Topic :: Text Processing :: Markup",
    "Topic :: Utilities",
    "Typing :: Typed",
    "Framework :: FastAPI",
    "Framework :: Flask",
    "Framework :: Django",
]
dependencies = [
    "mermaid-py>=0.8.0",
    "requests>=2.25.0",
    "typing-extensions>=4.0.0; python_version<'3.10'",
    "jinja2>=3.0.0",
    "jsonschema>=4.0.0",
    "openai>=1.98.0",
    "anthropic>=0.60.0",
    "ipython>=8.18.1",
    "uvicorn>=0.35.0",
    "fastapi>=0.116.1",
    "cairosvg>=2.8.2",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-xdist>=3.0.0",  # Parallel test execution
    "pytest-mock>=3.10.0",  # Mocking utilities
    "black>=23.0.0",
    "ruff>=0.1.0",
    "mypy>=1.0.0",
    "types-requests>=2.25.0",
    "types-setuptools>=68.0.0",
    "pre-commit>=3.0.0",
    "build>=0.10.0",
    "twine>=4.0.0",
    "safety>=2.0.0",
    "bandit>=1.7.0",
]
test = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-xdist>=3.0.0",
    "pytest-mock>=3.10.0",
    "coverage[toml]>=7.0.0",
]
cache = [
    "redis>=4.0.0",
    "diskcache>=5.6.0",  # Alternative file-based cache
]
interactive = [
    "fastapi>=0.100.0",
    "uvicorn[standard]>=0.20.0",
    "websockets>=11.0.0",
    "jinja2>=3.0.0",
    "python-multipart>=0.0.6",  # For file uploads
]
ai = [
    "openai>=1.0.0",
    "anthropic>=0.3.0",
    "spacy>=3.4.0",
    "networkx>=3.0.0",
    "tiktoken>=0.5.0",  # Token counting for AI APIs
]
collaboration = [
    "gitpython>=3.1.0",
    "websockets>=11.0.0",
    "sqlalchemy>=2.0.0",
    "alembic>=1.12.0",  # Database migrations
]
docs = [
    "sphinx>=5.0.0",
    "sphinx-rtd-theme>=1.0.0",
    "sphinx-autodoc-typehints>=1.24.0",
    "myst-parser>=2.0.0",  # Markdown support
    "sphinx-copybutton>=0.5.0",  # Copy code buttons
]
pdf = [
    "cairosvg>=2.7.0",  # SVG to PDF conversion
    "reportlab>=4.0.0",  # Alternative PDF generation
]
performance = [
    "cython>=3.0.0",  # Performance optimizations
    "numba>=0.58.0",  # JIT compilation
]
all = [
    "mermaid-render[cache,interactive,ai,collaboration,docs,pdf,performance]"
]

[project.urls]
Homepage = "https://github.com/mermaid-render/mermaid-render"
Documentation = "https://mermaid-render.readthedocs.io"
Repository = "https://github.com/mermaid-render/mermaid-render"
Issues = "https://github.com/mermaid-render/mermaid-render/issues"
Discussions = "https://github.com/mermaid-render/mermaid-render/discussions"
Changelog = "https://github.com/mermaid-render/mermaid-render/blob/main/CHANGELOG.md"
"Bug Reports" = "https://github.com/mermaid-render/mermaid-render/issues/new?template=bug_report.yml"
"Feature Requests" = "https://github.com/mermaid-render/mermaid-render/issues/new?template=feature_request.yml"
"Security" = "https://github.com/mermaid-render/mermaid-render/security/policy"
"Funding" = "https://github.com/sponsors/mermaid-render"

[project.scripts]
mermaid-render = "mermaid_render.cli:main"

[project.entry-points."mermaid_render.plugins"]
# Plugin entry points for extensibility

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["mermaid_render"]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.ruff]
target-version = "py38"
line-length = 88
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.per-file-ignores]
"__init__.py" = ["F401"]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "slow: Slow tests",
    "network: Tests requiring network access",
    "svg: SVG rendering tests",
    "browser: Browser compatibility tests",
    "error_handling: Error handling tests",
    "theme: Theme support tests",
    "export: Export functionality tests",
    "remote: Remote rendering tests",
    "performance: Performance tests",
]

[tool.coverage.run]
source = ["mermaid_render"]
omit = ["*/tests/*", "*/test_*"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
show_missing = true
skip_covered = false
precision = 2

[tool.coverage.html]
directory = "htmlcov"

[tool.coverage.xml]
output = "coverage.xml"

# Bandit security linting configuration
[tool.bandit]
exclude_dirs = ["tests", "docs", "examples"]
skips = ["B101", "B601"]  # Skip assert_used and shell_injection_process_args

# Safety dependency checking
[tool.safety]
ignore = []  # Add CVE IDs to ignore if needed



# Setuptools configuration for building
[tool.setuptools]
zip-safe = false
include-package-data = true

[tool.setuptools.packages.find]
where = ["."]
include = ["mermaid_render*"]
exclude = ["tests*", "docs*", "examples*"]

[tool.setuptools.package-data]
mermaid_render = ["py.typed", "*.json", "*.yaml", "*.yml"]

[dependency-groups]
dev = [
    "types-requests>=2.32.4.20250611",
]

[[tool.mypy.overrides]]
module = ["untyped_package.*"]
follow_untyped_imports = true