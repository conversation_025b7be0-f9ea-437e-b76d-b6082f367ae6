name: Update Dependencies

on:
  schedule:
    # Run weekly on Sundays at 12:00 UTC
    - cron: '0 12 * * 0'
  workflow_dispatch: # Allow manual trigger

jobs:
  update-dependencies:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install uv
      uses: astral-sh/setup-uv@v2
      
    - name: Update dependencies
      run: |
        # Update uv.lock
        uv lock --upgrade
        
        # Update pre-commit hooks
        pre-commit autoupdate || true
    
    - name: Check for changes
      id: verify-changed-files
      run: |
        if git diff --quiet; then
          echo "changed=false" >> $GITHUB_OUTPUT
        else
          echo "changed=true" >> $GITHUB_OUTPUT
        fi
    
    - name: Create Pull Request
      if: steps.verify-changed-files.outputs.changed == 'true'
      uses: peter-evans/create-pull-request@v5
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        commit-message: 'chore: update dependencies'
        title: 'chore: update dependencies'
        body: |
          ## Automated Dependency Update
          
          This PR updates project dependencies to their latest versions.
          
          ### Changes
          - Updated `uv.lock` with latest package versions
          - Updated pre-commit hooks to latest versions
          
          ### Testing
          Please review the changes and ensure all tests pass before merging.
          
          ---
          *This PR was created automatically by the Update Dependencies workflow.*
        branch: chore/update-dependencies
        delete-branch: true
        draft: false
