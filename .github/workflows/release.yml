name: Release

on:
  push:
    tags:
      - 'v*'

permissions:
  contents: write
  id-token: write

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.11"

    - name: Install UV
      uses: astral-sh/setup-uv@v2

    - name: Install dependencies
      run: |
        uv sync --all-extras

    - name: Run tests
      run: |
        uv run pytest --cov=mermaid_render

    - name: Run linting
      run: |
        uv run black --check mermaid_render tests
        uv run ruff check mermaid_render tests
        uv run mypy mermaid_render

  build:
    runs-on: ubuntu-latest
    needs: test
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.11"

    - name: Install UV
      uses: astral-sh/setup-uv@v2

    - name: Install build dependencies
      run: |
        uv tool install build

    - name: Build package
      run: |
        uv tool run build

    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: dist
        path: dist/

  publish:
    runs-on: ubuntu-latest
    needs: build
    environment: release
    steps:
    - uses: actions/checkout@v4

    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: dist
        path: dist/

    - name: Publish to PyPI
      uses: pypa/gh-action-pypi-publish@release/v1
      with:
        password: ${{ secrets.PYPI_API_TOKEN }}

  github-release:
    runs-on: ubuntu-latest
    needs: publish
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: dist
        path: dist/

    - name: Generate changelog
      id: changelog
      run: |
        # Extract version from tag
        VERSION=${GITHUB_REF#refs/tags/v}
        echo "version=$VERSION" >> $GITHUB_OUTPUT
        
        # Extract changelog for this version
        if [ -f "CHANGELOG.md" ]; then
          # Get changelog section for this version
          sed -n "/## \[$VERSION\]/,/## \[/p" CHANGELOG.md | sed '$d' > release_notes.md
          if [ ! -s release_notes.md ]; then
            echo "Release $VERSION" > release_notes.md
            echo "" >> release_notes.md
            echo "See [CHANGELOG.md](CHANGELOG.md) for details." >> release_notes.md
          fi
        else
          echo "Release $VERSION" > release_notes.md
        fi

    - name: Create GitHub Release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ github.ref }}
        release_name: Release ${{ steps.changelog.outputs.version }}
        body_path: release_notes.md
        draft: false
        prerelease: false

    - name: Upload Release Assets
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ steps.create_release.outputs.upload_url }}
        asset_path: dist/
        asset_name: mermaid-render-${{ steps.changelog.outputs.version }}.tar.gz
        asset_content_type: application/gzip
