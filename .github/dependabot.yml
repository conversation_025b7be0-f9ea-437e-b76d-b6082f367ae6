name: Dependency Updates
on:
  schedule:
    - cron: '0 7 * * 1'  # Every Monday at 7:00 UTC
  workflow_dispatch:

permissions:
  contents: read
  pull-requests: write

jobs:
  dependabot:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Update Dependencies
        uses: dependabot/fetch-metadata@v2
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
