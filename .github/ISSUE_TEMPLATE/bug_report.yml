name: Bug Report
description: File a bug report to help us improve
title: "[Bug]: "
labels: ["bug", "triage"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report!

  - type: input
    id: version
    attributes:
      label: Version
      description: What version of Mermaid Render are you using?
      placeholder: "1.0.0"
    validations:
      required: true

  - type: textarea
    id: description
    attributes:
      label: Bug Description
      description: A clear and concise description of what the bug is.
      placeholder: Tell us what happened!
    validations:
      required: true

  - type: textarea
    id: reproduction
    attributes:
      label: Steps to Reproduce
      description: Steps to reproduce the behavior
      placeholder: |
        1. Create a diagram with...
        2. Call renderer.render()...
        3. See error...
    validations:
      required: true

  - type: textarea
    id: expected
    attributes:
      label: Expected Behavior
      description: A clear and concise description of what you expected to happen.
    validations:
      required: true

  - type: textarea
    id: actual
    attributes:
      label: Actual Behavior
      description: A clear and concise description of what actually happened.
    validations:
      required: true

  - type: textarea
    id: code
    attributes:
      label: Code Sample
      description: Minimal code sample that reproduces the issue
      render: python
      placeholder: |
        from mermaid_render import MermaidRenderer
        
        # Your code here
    validations:
      required: false

  - type: textarea
    id: error
    attributes:
      label: Error Output
      description: Full error message and traceback (if applicable)
      render: shell
    validations:
      required: false

  - type: dropdown
    id: python-version
    attributes:
      label: Python Version
      description: What version of Python are you using?
      options:
        - "3.12"
        - "3.11"
        - "3.10"
        - "3.9"
        - "3.8"
        - "Other (please specify in additional context)"
    validations:
      required: true

  - type: dropdown
    id: os
    attributes:
      label: Operating System
      description: What operating system are you using?
      options:
        - "Linux"
        - "macOS"
        - "Windows"
        - "Other (please specify in additional context)"
    validations:
      required: true

  - type: textarea
    id: environment
    attributes:
      label: Environment Details
      description: |
        Additional environment information (pip list, virtual environment, etc.)
      render: shell
    validations:
      required: false

  - type: textarea
    id: additional
    attributes:
      label: Additional Context
      description: Add any other context about the problem here.
    validations:
      required: false

  - type: checkboxes
    id: checklist
    attributes:
      label: Checklist
      description: Please confirm the following
      options:
        - label: I have searched existing issues to ensure this is not a duplicate
          required: true
        - label: I have provided a minimal code sample that reproduces the issue
          required: false
        - label: I have included the full error message and traceback
          required: false
