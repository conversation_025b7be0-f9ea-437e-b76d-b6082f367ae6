name: Feature Request
description: Suggest an idea for this project
title: "[Feature]: "
labels: ["enhancement", "triage"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        Thanks for suggesting a new feature! Please provide as much detail as possible.

  - type: textarea
    id: problem
    attributes:
      label: Problem Statement
      description: Is your feature request related to a problem? Please describe.
      placeholder: I'm always frustrated when...
    validations:
      required: true

  - type: textarea
    id: solution
    attributes:
      label: Proposed Solution
      description: Describe the solution you'd like to see implemented.
      placeholder: I would like to see...
    validations:
      required: true

  - type: textarea
    id: alternatives
    attributes:
      label: Alternatives Considered
      description: Describe any alternative solutions or features you've considered.
    validations:
      required: false

  - type: textarea
    id: use-case
    attributes:
      label: Use Case
      description: Describe your specific use case and how this feature would help.
      placeholder: |
        I need this feature because...
        It would help me to...
    validations:
      required: true

  - type: textarea
    id: example
    attributes:
      label: Example Usage
      description: Show how you would like to use this feature
      render: python
      placeholder: |
        # Example of how the feature might work
        from mermaid_render import NewFeature
        
        feature = NewFeature()
        result = feature.do_something()
    validations:
      required: false

  - type: dropdown
    id: priority
    attributes:
      label: Priority
      description: How important is this feature to you?
      options:
        - "Low - Nice to have"
        - "Medium - Would be helpful"
        - "High - Important for my use case"
        - "Critical - Blocking my work"
    validations:
      required: true

  - type: dropdown
    id: complexity
    attributes:
      label: Estimated Complexity
      description: How complex do you think this feature would be to implement?
      options:
        - "Low - Simple addition"
        - "Medium - Moderate changes required"
        - "High - Significant implementation effort"
        - "Unknown - Not sure"
    validations:
      required: false

  - type: checkboxes
    id: feature-type
    attributes:
      label: Feature Type
      description: What type of feature is this? (Select all that apply)
      options:
        - label: New diagram type
        - label: New output format
        - label: API enhancement
        - label: Performance improvement
        - label: Developer experience
        - label: Documentation
        - label: Configuration option
        - label: Integration with external service

  - type: textarea
    id: additional
    attributes:
      label: Additional Context
      description: Add any other context, screenshots, or examples about the feature request here.
    validations:
      required: false

  - type: checkboxes
    id: contribution
    attributes:
      label: Contribution
      description: Are you willing to contribute to this feature?
      options:
        - label: I would like to implement this feature myself
        - label: I can help with testing
        - label: I can help with documentation
        - label: I can provide feedback during development

  - type: checkboxes
    id: checklist
    attributes:
      label: Checklist
      description: Please confirm the following
      options:
        - label: I have searched existing issues to ensure this is not a duplicate
          required: true
        - label: I have provided a clear use case for this feature
          required: true
        - label: I have considered how this feature fits with the existing API
          required: false
