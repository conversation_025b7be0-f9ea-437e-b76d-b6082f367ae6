# Pull Request

## Description

Brief description of the changes in this PR.

Fixes #(issue number)

## Type of Change

Please delete options that are not relevant.

- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update
- [ ] Performance improvement
- [ ] Code refactoring
- [ ] Test improvements
- [ ] CI/CD improvements

## Changes Made

- [ ] Change 1
- [ ] Change 2
- [ ] Change 3

## Testing

- [ ] Tests pass locally (`make test`)
- [ ] New tests added for new functionality
- [ ] Manual testing completed
- [ ] Integration tests updated (if applicable)
- [ ] Performance impact assessed (if applicable)

### Test Coverage

- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] Test coverage maintained or improved

## Code Quality

- [ ] Code follows the style guidelines (`make lint`)
- [ ] Self-review of code completed
- [ ] Code is well-commented, particularly in hard-to-understand areas
- [ ] Type hints added for new functions/methods
- [ ] Docstrings added/updated for public APIs

## Documentation

- [ ] Documentation updated (if applicable)
- [ ] README updated (if applicable)
- [ ] CHANGELOG.md updated
- [ ] API documentation updated (if applicable)
- [ ] Examples updated (if applicable)

## Breaking Changes

If this PR introduces breaking changes, please describe them here:

- Breaking change 1
- Breaking change 2

## Migration Guide

If breaking changes are introduced, provide migration instructions:

```python
# Before
old_api_usage()

# After
new_api_usage()
```

## Screenshots/Examples

If applicable, add screenshots or code examples to help explain your changes.

```python
# Example of new functionality
from mermaid_render import NewFeature

feature = NewFeature()
result = feature.new_method()
```

## Performance Impact

- [ ] No performance impact
- [ ] Performance improved
- [ ] Performance impact assessed and acceptable
- [ ] Performance benchmarks included

## Dependencies

- [ ] No new dependencies added
- [ ] New dependencies are justified and documented
- [ ] Dependencies updated (list versions)

## Checklist

- [ ] My code follows the style guidelines of this project
- [ ] I have performed a self-review of my own code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
- [ ] I have added tests that prove my fix is effective or that my feature works
- [ ] New and existing unit tests pass locally with my changes
- [ ] Any dependent changes have been merged and published in downstream modules

## Additional Notes

Add any additional notes, concerns, or questions for reviewers here.

## Reviewer Guidelines

For reviewers, please check:

- [ ] Code quality and style
- [ ] Test coverage and quality
- [ ] Documentation completeness
- [ ] Performance implications
- [ ] Security considerations
- [ ] API design consistency
- [ ] Backward compatibility
