# Dependabot configuration for automated dependency updates
# See: https://docs.github.com/en/code-security/dependabot/dependabot-version-updates/configuration-options-for-the-dependabot.yml-file
# Note: Rename this file to dependabot.yml to enable Dependabot

version: 2
updates:
  # Python dependencies
  - package-ecosystem: "pip"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 10
    reviewers:
      - "mermaid-render/maintainers"
    assignees:
      - "mermaid-render/maintainers"
    commit-message:
      prefix: "deps"
      prefix-development: "deps-dev"
      include: "scope"
    labels:
      - "dependencies"
      - "python"
    milestone: "Next Release"
    
    # Grouping strategy for related updates
    groups:
      # Core dependencies
      core-dependencies:
        patterns:
          - "mermaid-py"
          - "requests"
          - "jinja2"
          - "jsonschema"
      
      # Development dependencies
      dev-dependencies:
        patterns:
          - "pytest*"
          - "black"
          - "ruff"
          - "mypy"
          - "pre-commit"
          - "coverage"
          - "bandit"
          - "safety"
      
      # AI dependencies
      ai-dependencies:
        patterns:
          - "openai"
          - "anthropic"
          - "tiktoken"
          - "spacy"
          - "networkx"
      
      # Web dependencies
      web-dependencies:
        patterns:
          - "fastapi"
          - "uvicorn"
          - "websockets"
          - "python-multipart"
      
      # Documentation dependencies
      docs-dependencies:
        patterns:
          - "sphinx*"
          - "myst-parser"
    
    # Version update strategy
    versioning-strategy: "increase"
    
    # Ignore specific dependencies or versions
    ignore:
      # Ignore major version updates for stable dependencies
      - dependency-name: "mermaid-py"
        update-types: ["version-update:semver-major"]
      
      # Ignore patch updates for frequently updated packages
      - dependency-name: "openai"
        update-types: ["version-update:semver-patch"]
      
      # Ignore specific versions with known issues
      - dependency-name: "*"
        versions: ["1.0.0-alpha", "1.0.0-beta"]

  # GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "tuesday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 5
    reviewers:
      - "mermaid-render/maintainers"
    commit-message:
      prefix: "ci"
      include: "scope"
    labels:
      - "dependencies"
      - "github-actions"
      - "ci"
