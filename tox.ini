[tox]
envlist = py{39,310,311,312}, lint, type-check, security, docs
isolated_build = true
skip_missing_interpreters = true

[testenv]
deps = 
    pytest>=7.0.0
    pytest-cov>=4.0.0
    pytest-xdist>=3.0.0
    pytest-mock>=3.10.0
extras = test
commands = 
    pytest {posargs}

[testenv:coverage]
deps = 
    {[testenv]deps}
    coverage[toml]>=7.0.0
commands = 
    pytest --cov=mermaid_render --cov-report=html --cov-report=term --cov-report=xml

[testenv:lint]
deps = 
    black>=23.0.0
    ruff>=0.1.0
commands = 
    black --check mermaid_render tests
    ruff check mermaid_render tests

[testenv:format]
deps = 
    black>=23.0.0
    ruff>=0.1.0
commands = 
    black mermaid_render tests examples
    ruff check --fix mermaid_render tests examples

[testenv:type-check]
deps = 
    mypy>=1.0.0
    types-requests>=2.25.0
    types-setuptools>=68.0.0
commands = 
    mypy mermaid_render

[testenv:security]
deps = 
    safety>=2.0.0
    bandit>=1.7.0
commands = 
    safety check
    bandit -r mermaid_render/

[testenv:docs]
deps = 
    sphinx>=5.0.0
    sphinx-rtd-theme>=1.0.0
    sphinx-autodoc-typehints>=1.24.0
    myst-parser>=2.0.0
    sphinx-copybutton>=0.5.0
extras = docs
commands = 
    sphinx-build -b html docs docs/_build/html

[testenv:build]
deps = 
    build>=0.10.0
    twine>=4.0.0
commands = 
    python -m build
    twine check dist/*

[testenv:integration]
deps = 
    {[testenv]deps}
extras = cache,interactive
commands = 
    pytest -m integration {posargs}

[testenv:performance]
deps = 
    {[testenv]deps}
    pytest-benchmark>=4.0.0
extras = performance
commands = 
    pytest -m "not slow" --benchmark-only {posargs}

# Configuration for pytest when run via tox
[pytest]
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*
markers = 
    unit: Unit tests
    integration: Integration tests
    slow: Slow tests
    network: Tests requiring network access
addopts = -ra -q --strict-markers --strict-config

# Flake8 configuration (if used)
[flake8]
max-line-length = 88
extend-ignore = E203, W503
exclude = 
    .git,
    __pycache__,
    .tox,
    .venv,
    venv,
    build,
    dist,
    *.egg-info

# Coverage configuration
[coverage:run]
source = mermaid_render
omit = 
    */tests/*
    */test_*
    mermaid_render/cli.py

[coverage:report]
exclude_lines = 
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod
show_missing = true
skip_covered = false
precision = 2
