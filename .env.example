# Mermaid Render Environment Configuration
# Copy this file to .env and customize for your environment

# =============================================================================
# Core Configuration
# =============================================================================

# Default theme for rendering
MERMAID_DEFAULT_THEME=default

# Default output format (svg, png, pdf)
MERMAID_DEFAULT_FORMAT=svg

# Default output directory for saved diagrams
MERMAID_OUTPUT_DIR=./output

# Enable caching for improved performance
MERMAID_CACHE_ENABLED=true

# Cache backend (memory, file, redis)
MERMAID_CACHE_BACKEND=memory

# Cache TTL in seconds (3600 = 1 hour)
MERMAID_CACHE_TTL=3600

# =============================================================================
# Rendering Configuration
# =============================================================================

# Default diagram width in pixels
MERMAID_DEFAULT_WIDTH=800

# Default diagram height in pixels
MERMAID_DEFAULT_HEIGHT=600

# Rendering timeout in seconds
MERMAID_RENDER_TIMEOUT=30

# Background color for diagrams
MERMAID_BACKGROUND_COLOR=white

# Scale factor for high-DPI displays
MERMAID_SCALE_FACTOR=1.0

# =============================================================================
# AI Features Configuration
# =============================================================================

# OpenAI API configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=2000

# Anthropic API configuration
ANTHROPIC_API_KEY=your_anthropic_api_key_here
ANTHROPIC_MODEL=claude-3-sonnet-20240229

# Enable AI-powered diagram generation
MERMAID_AI_ENABLED=false

# AI processing timeout in seconds
MERMAID_AI_TIMEOUT=60

# =============================================================================
# Interactive Features Configuration
# =============================================================================

# Interactive server configuration
MERMAID_INTERACTIVE_HOST=localhost
MERMAID_INTERACTIVE_PORT=8080
MERMAID_INTERACTIVE_DEBUG=false

# WebSocket configuration
MERMAID_WEBSOCKET_ENABLED=true
MERMAID_WEBSOCKET_PORT=8081

# =============================================================================
# Cache Configuration
# =============================================================================

# Redis configuration (if using Redis cache backend)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# File cache configuration
MERMAID_CACHE_DIR=./.mermaid_cache

# Memory cache size limit (in MB)
MERMAID_MEMORY_CACHE_SIZE=100

# =============================================================================
# Collaboration Features Configuration
# =============================================================================

# Database configuration for collaboration features
DATABASE_URL=sqlite:///mermaid_collaboration.db

# Git integration
GIT_ENABLED=false
GIT_REPO_PATH=./

# Version control settings
VERSION_CONTROL_ENABLED=false
VERSION_CONTROL_BACKEND=git

# =============================================================================
# Development Configuration
# =============================================================================

# Development mode (enables debug features)
MERMAID_DEV_MODE=false

# Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
MERMAID_LOG_LEVEL=INFO

# Log file path
MERMAID_LOG_FILE=./logs/mermaid_render.log

# Enable performance profiling
MERMAID_PROFILING_ENABLED=false

# Profiling output directory
MERMAID_PROFILING_DIR=./profiling

# =============================================================================
# Security Configuration
# =============================================================================

# Enable security features
MERMAID_SECURITY_ENABLED=true

# Maximum file size for uploads (in MB)
MERMAID_MAX_FILE_SIZE=10

# Allowed file extensions for uploads
MERMAID_ALLOWED_EXTENSIONS=.mmd,.mermaid,.txt

# Rate limiting (requests per minute)
MERMAID_RATE_LIMIT=60

# =============================================================================
# Export Configuration
# =============================================================================

# PDF export configuration
PDF_ENGINE=cairosvg
PDF_DPI=300

# PNG export configuration
PNG_DPI=300
PNG_QUALITY=95

# SVG optimization
SVG_OPTIMIZE=true

# =============================================================================
# Performance Configuration
# =============================================================================

# Enable parallel processing
MERMAID_PARALLEL_ENABLED=true

# Number of worker processes (0 = auto-detect)
MERMAID_WORKER_PROCESSES=0

# Memory limit per worker (in MB)
MERMAID_WORKER_MEMORY_LIMIT=512

# Enable JIT compilation (requires numba)
MERMAID_JIT_ENABLED=false

# =============================================================================
# Testing Configuration
# =============================================================================

# Test database URL
TEST_DATABASE_URL=sqlite:///test_mermaid.db

# Test cache backend
TEST_CACHE_BACKEND=memory

# Enable test coverage
TEST_COVERAGE_ENABLED=true

# Test timeout in seconds
TEST_TIMEOUT=300

# =============================================================================
# Monitoring and Analytics
# =============================================================================

# Enable metrics collection
MERMAID_METRICS_ENABLED=false

# Metrics backend (prometheus, statsd)
MERMAID_METRICS_BACKEND=prometheus

# Metrics port
MERMAID_METRICS_PORT=9090

# Enable error tracking
MERMAID_ERROR_TRACKING_ENABLED=false

# Error tracking service (sentry)
SENTRY_DSN=

# =============================================================================
# External Services
# =============================================================================

# Mermaid.js service URL (for remote rendering)
MERMAID_SERVICE_URL=https://mermaid.ink

# Backup service configuration
BACKUP_ENABLED=false
BACKUP_PROVIDER=s3
BACKUP_BUCKET=mermaid-diagrams
BACKUP_ACCESS_KEY=
BACKUP_SECRET_KEY=

# CDN configuration
CDN_ENABLED=false
CDN_BASE_URL=https://cdn.example.com/diagrams/

# =============================================================================
# Feature Flags
# =============================================================================

# Enable experimental features
MERMAID_EXPERIMENTAL_FEATURES=false

# Enable beta features
MERMAID_BETA_FEATURES=false

# Feature-specific flags
FEATURE_ADVANCED_THEMES=true
FEATURE_CUSTOM_PLUGINS=false
FEATURE_REAL_TIME_COLLABORATION=false
FEATURE_DIAGRAM_TEMPLATES=true
FEATURE_BATCH_PROCESSING=true
